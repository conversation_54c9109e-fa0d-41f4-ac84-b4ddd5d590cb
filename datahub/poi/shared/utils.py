import time
import logging
import requests
import pandas as pd
from config import NAME_FIELDS

# Set up logging
logging.basicConfig(level=logging.INFO,
                    format='%(asctime)s %(levelname)s %(message)s')
logger = logging.getLogger("poi_utils")


def exponential_backoff(retries):
    """Calculate exponential backoff delay"""
    return min(60, (2 ** retries) + (0.1 * retries))


def has_any_name(row):
    """Check if row has any name field filled"""
    return any(str(row[field]).strip() for field in NAME_FIELDS if field in row)


def has_lat_lon(row):
    """Check if row has valid latitude and longitude"""
    try:
        lat = float(row['latitude'])
        lon = float(row['longitude'])
        return lat != 0 and lon != 0
    except (KeyError, ValueError, TypeError):
        return False


def get_country_from_coordinates(lat, lon, max_retries=3, delay=1):
    """
    Get country name from latitude and longitude using Nominatim reverse geocoding.
    For Turkey POI data, we use simple bounds checking first.
    """
    # Simple bounds check for Türkiye (approximate)
    # Türkiye bounds: lat 36-42, lon 26-45
    if 36 <= lat <= 42 and 26 <= lon <= 45:
        return 'Türkiye'

    # If coordinates are outside Turkey bounds, try reverse geocoding
    try:
        url = f"https://nominatim.openstreetmap.org/reverse?format=json&lat={lat}&lon={lon}&zoom=3"
        headers = {'User-Agent': 'Wizlop-POI-Processor/1.0'}

        for attempt in range(max_retries):
            try:
                response = requests.get(url, headers=headers, timeout=10)
                if response.status_code == 200:
                    data = response.json()
                    if 'address' in data and 'country' in data['address']:
                        return data['address']['country']
                time.sleep(delay)
            except Exception as e:
                logger.warning(
                    f"Attempt {attempt + 1} failed for coordinates {lat}, {lon}: {e}")
                if attempt < max_retries - 1:
                    time.sleep(delay * (attempt + 1))
    except Exception as e:
        logger.error(
            f"Error getting country for coordinates {lat}, {lon}: {e}")

    return 'Unknown'


def build_overpass_query(city, key, value):
    """Build Overpass API query for extracting POIs"""
    return f'''
    [out:json][timeout:60];
    area["name"="{city}"]->.searchArea;
    (
      node[{key}="{value}"](area.searchArea);
      way[{key}="{value}"](area.searchArea);
      relation[{key}="{value}"](area.searchArea);
    );
    out center meta;
    '''


def make_overpass_request(query, max_retries=3):
    """Make request to Overpass API with retry logic"""
    from config import OVERPASS_URL

    for attempt in range(max_retries):
        try:
            response = requests.post(OVERPASS_URL, data=query, timeout=120)
            if response.status_code == 200:
                return response.json()
            elif response.status_code == 429:  # Rate limited
                wait_time = exponential_backoff(attempt)
                logger.warning(f"Rate limited. Waiting {wait_time} seconds...")
                time.sleep(wait_time)
            else:
                logger.error(f"HTTP {response.status_code}: {response.text}")
                return None
        except requests.exceptions.Timeout:
            logger.warning(f"Timeout on attempt {attempt + 1}")
            if attempt < max_retries - 1:
                time.sleep(exponential_backoff(attempt))
        except Exception as e:
            logger.error(f"Request failed on attempt {attempt + 1}: {e}")
            if attempt < max_retries - 1:
                time.sleep(exponential_backoff(attempt))

    return None


def clean_phone_number(phone):
    """Clean and standardize phone numbers"""
    if pd.isna(phone) or phone == '':
        return ''

    phone = str(phone).strip()
    # Remove common separators and spaces
    phone = phone.replace(' ', '').replace(
        '-', '').replace('(', '').replace(')', '')

    # Add Turkish country code if missing
    if phone.startswith('0') and len(phone) == 11:
        phone = '+90' + phone[1:]
    elif phone.startswith('90') and len(phone) == 12:
        phone = '+' + phone
    elif not phone.startswith('+') and len(phone) == 10:
        phone = '+90' + phone

    return phone


def validate_poi_data(df):
    """Validate POI dataframe and return cleaned version"""
    logger.info(f"Validating {len(df)} POI records...")

    # Remove rows without names or coordinates
    df_clean = df[df.apply(lambda row: has_any_name(row)
                           and has_lat_lon(row), axis=1)]

    # Clean phone numbers
    if 'phone_number' in df_clean.columns:
        df_clean['phone_number'] = df_clean['phone_number'].apply(
            clean_phone_number)

    # Remove duplicates based on coordinates (within 10 meters)
    df_clean = df_clean.drop_duplicates(subset=['latitude', 'longitude'])

    logger.info(
        f"Validation complete: {len(df)} -> {len(df_clean)} records kept")
    return df_clean
