#!/usr/bin/env python3
"""
Shared Database Enrichment Module

Enriches POI data with spatial database information including:
- Administrative boundaries (province, city, district)
- Nearest street information
- Complete formatted addresses

This module is shared across all POI types.
"""

import pandas as pd
import psycopg2
import logging
import os
from pathlib import Path
from config import DB_CONFIG
from tqdm import tqdm

# Set up logging
logging.basicConfig(level=logging.INFO,
                    format='%(asctime)s %(levelname)s %(message)s')
logger = logging.getLogger("db_enrichment")


class DatabaseEnricher:
    """Class for enriching POI data with spatial database information"""

    def __init__(self):
        self.connection = None
        self.cursor = None

    def connect(self):
        """Connect to the spatial database"""
        try:
            self.connection = psycopg2.connect(
                host=DB_CONFIG['DB_HOST'],
                port=DB_CONFIG['DB_PORT'],
                database=DB_CONFIG['DB_NAME'],
                user=DB_CONFIG['DB_USER'],
                password=DB_CONFIG['DB_PASSWORD']
            )
            # Set autocommit to avoid transaction issues
            self.connection.autocommit = True
            self.cursor = self.connection.cursor()
            logger.info("Successfully connected to spatial database")
            return True
        except Exception as e:
            logger.error(f"Failed to connect to database: {e}")
            return False

    def disconnect(self):
        """Disconnect from the database"""
        if self.cursor:
            self.cursor.close()
        if self.connection:
            self.connection.close()
        logger.info("Disconnected from database")

    def get_administrative_info(self, lat, lon):
        """Get administrative boundary information for a point

        Returns Turkish administrative hierarchy:
        - Province (İl): admin_level 4 - e.g., İstanbul
        - District (İlçe): admin_level 6 - e.g., Esenyurt
        - Neighborhood (Mahalle): admin_level 8 - e.g., Akevler Mahallesi

        Returns:
            tuple: (province, district, neighborhood)
        """
        if not self.cursor:
            return None, None, None

        try:
            # Query for administrative boundaries with correct Turkish hierarchy
            # admin_level 4: Province (İl) - e.g., İstanbul
            # admin_level 6: District (İlçe) - e.g., Esenyurt
            # admin_level 8: Neighborhood (Mahalle) - e.g., Akevler Mahallesi
            query = """
            SELECT
                admin4.name as province,
                admin6.name as district,
                admin8.name as neighborhood
            FROM
                (SELECT ST_SetSRID(ST_MakePoint(%s, %s), 4326) as point) as p
            LEFT JOIN spatial_schema.admin_boundaries admin4 ON
                admin4.admin_level = 4 AND ST_Contains(admin4.geom, p.point)
            LEFT JOIN spatial_schema.admin_boundaries admin6 ON
                admin6.admin_level = 6 AND ST_Contains(admin6.geom, p.point)
            LEFT JOIN spatial_schema.admin_boundaries admin8 ON
                admin8.admin_level = 8 AND ST_Contains(admin8.geom, p.point)
            """

            self.cursor.execute(query, (lon, lat))
            result = self.cursor.fetchone()

            if result:
                # province, district, neighborhood
                return result[0], result[1], result[2]
            else:
                return None, None, None

        except Exception as e:
            logger.warning(
                f"Error getting administrative info for {lat}, {lon}: {e}")
            # Rollback any failed transaction
            if self.connection and not self.connection.autocommit:
                try:
                    self.connection.rollback()
                except:
                    pass
            return None, None, None

    def get_nearest_street(self, lat, lon, max_distance=500):
        """Get nearest street name within max_distance meters"""
        if not self.cursor:
            return None

        try:
            # Query for nearest street
            query = """
            SELECT name
            FROM spatial_schema.roads
            WHERE name IS NOT NULL AND name != ''
            ORDER BY geom <-> ST_SetSRID(ST_MakePoint(%s, %s), 4326)
            LIMIT 1
            """

            self.cursor.execute(query, (lon, lat))
            result = self.cursor.fetchone()

            if result:
                return result[0]
            else:
                return None

        except Exception as e:
            logger.warning(
                f"Error getting nearest street for {lat}, {lon}: {e}")
            # Rollback any failed transaction
            if self.connection and not self.connection.autocommit:
                try:
                    self.connection.rollback()
                except:
                    pass
            return None

    def format_full_address(self, street, neighborhood, district, province):
        """Format a complete address from Turkish administrative components

        Args:
            street: Street name
            neighborhood: Mahalle (admin level 8)
            district: İlçe (admin level 6) - e.g., Esenyurt
            province: İl (admin level 4) - e.g., İstanbul
        """
        address_parts = []

        if street:
            address_parts.append(street)
        if neighborhood and neighborhood != district:
            address_parts.append(neighborhood)
        if district and district != province:
            address_parts.append(district)
        if province:
            address_parts.append(province)

        return ", ".join(address_parts) if address_parts else ""

    def enrich_poi_dataframe(self, df):
        """Enrich a POI dataframe with spatial database information"""
        if not self.connect():
            logger.error("Cannot enrich data without database connection")
            return df

        logger.info(
            f"Enriching {len(df)} POI records with spatial database...")

        # Add new columns if they don't exist, ensuring they have string dtype
        # Updated to use correct Turkish administrative hierarchy
        # Note: We'll rename city_admin to district and district to neighborhood in the final CSV
        for col in ['province', 'city_admin', 'district', 'street', 'full_address']:
            if col not in df.columns:
                df[col] = pd.Series([''] * len(df), dtype='string')
            else:
                # Convert existing columns to string dtype if they're not already
                df[col] = df[col].astype('string')

        enriched_count = 0

        # Process with progress bar
        with tqdm(total=len(df), desc="Enriching POIs", unit="record") as pbar:
            for idx, row in df.iterrows():
                try:
                    lat, lon = float(row['latitude']), float(row['longitude'])

                    # Get administrative boundaries (corrected hierarchy)
                    province, district, neighborhood = self.get_administrative_info(
                        lat, lon)

                    # Get nearest street
                    street = self.get_nearest_street(lat, lon)

                    # Update dataframe with proper string values
                    # Map to CSV column names (will be renamed later for import)
                    if province:
                        df.at[idx, 'province'] = str(province)
                    if district:  # This is İlçe level -> goes to city_admin column
                        df.at[idx, 'city_admin'] = str(district)
                    if neighborhood:  # This is Mahalle level -> goes to district column
                        df.at[idx, 'district'] = str(neighborhood)
                    if street:
                        df.at[idx, 'street'] = str(street)

                    # Format full address (using the actual hierarchy values)
                    full_address = self.format_full_address(
                        street, neighborhood, district, province)
                    if full_address:
                        df.at[idx, 'full_address'] = str(full_address)
                        enriched_count += 1

                except Exception as e:
                    logger.warning(f"Error enriching record {idx}: {e}")
                    continue

                pbar.update(1)

        # Rename columns to match import script expectations
        # The import script expects: city_admin=district(İlçe), district=neighborhood(Mahalle)
        # But we want to clean this up and use proper names
        df = self._standardize_column_names(df)

        self.disconnect()
        logger.info(
            f"Enrichment complete: {enriched_count}/{len(df)} records enriched")
        return df

    def _standardize_column_names(self, df):
        """Standardize column names for consistent import"""
        # Create a mapping to clean up the confusing column names
        column_mapping = {}

        # If we have the old confusing structure, clean it up
        if 'city_admin' in df.columns and 'district' in df.columns:
            # city_admin contains İlçe level data -> rename to district
            # district contains Mahalle level data -> rename to neighborhood
            column_mapping['city_admin'] = 'district'
            column_mapping['district'] = 'neighborhood'

        # Apply the renaming
        if column_mapping:
            df = df.rename(columns=column_mapping)
            logger.info(f"Renamed columns: {column_mapping}")

        return df


def enrich_csv_file(csv_path, output_path=None):
    """Enrich a single CSV file with spatial database information"""
    if output_path is None:
        output_path = csv_path

    try:
        # Load CSV
        df = pd.read_csv(csv_path)
        logger.info(f"Loaded {len(df)} records from {csv_path}")

        # Enrich data
        enricher = DatabaseEnricher()
        df_enriched = enricher.enrich_poi_dataframe(df)

        # Save enriched data
        df_enriched.to_csv(output_path, index=False, encoding='utf-8')
        logger.info(f"Saved enriched data to {output_path}")

        return True

    except Exception as e:
        logger.error(f"Error enriching CSV file {csv_path}: {e}")
        return False


def enrich_directory(directory_path):
    """Enrich all CSV files in a directory"""
    directory = Path(directory_path)

    if not directory.exists():
        logger.error(f"Directory does not exist: {directory_path}")
        return False

    csv_files = list(directory.rglob("*.csv"))

    if not csv_files:
        logger.warning(f"No CSV files found in {directory_path}")
        return True

    logger.info(f"Found {len(csv_files)} CSV files to enrich")

    success_count = 0
    for csv_file in csv_files:
        if enrich_csv_file(csv_file):
            success_count += 1
        else:
            logger.error(f"Failed to enrich {csv_file}")

    logger.info(
        f"Enrichment complete: {success_count}/{len(csv_files)} files processed successfully")
    return success_count == len(csv_files)
