from pathlib import Path
import os
import logging

# Root directory for POI extraction projects
ROOT_DIR = Path(__file__).resolve().parents[2]  # Points to datahub/
DATA_DIR = ROOT_DIR / "data"
RAW_DATA_DIR = DATA_DIR / "raw"
OUTPUT_DIR = DATA_DIR / "output"

# Path to the full Turkey OSM PBF file (should be the full file, not clipped)
TURKEY_OSM_PBF_PATH = RAW_DATA_DIR / "turkey-latest.osm.pbf"

# Language codes for extracting multilingual names
LANGUAGE_CODES = ['tr', 'en', 'de', 'ru', 'ar', 'fr', 'es', 'uk']

# # Dynamic city extraction from OSM data

# THE EXTARCT should be done in the code not here and should work with any country so super dynamic 

# Common data columns for POI extraction
COMMON_COLUMNS = [
    'latitude', 'longitude', 'city', 'district', 'neighborhood', 'street', 'phone_number', 'description',
    'opening_hours', 'cuisine', 'name', 'name_en', 'name_uk', 'name_de',
    'name_tr', 'name_ru', 'name_ar', 'category', 'subcategory', 'country', 'province'
]

# Name fields for validation
NAME_FIELDS = [
    'name', 'name_en', 'name_uk', 'name_de', 'name_tr', 'name_ru', 'name_ar'
]

# Overpass API configuration
OVERPASS_URL = "https://overpass-api.de/api/interpreter"

# Database configuration (for enrichment)
DB_CONFIG = {
    'DB_NAME': os.getenv('DB_NAME', 'wizlop_db'),
    'DB_USER': os.getenv('DB_USER', 'wizlop_user'),
    'DB_PASSWORD': os.getenv('DB_PASSWORD', 'wizlop_pass'),
    'DB_HOST': os.getenv('DB_HOST', 'localhost'),
    'DB_PORT': os.getenv('DB_PORT', '5432')
}

# Ensure output directories exist
RAW_DATA_DIR.mkdir(parents=True, exist_ok=True)
OUTPUT_DIR.mkdir(parents=True, exist_ok=True)


def get_poi_output_dir(poi_type):
    """Get output directory for specific POI type"""
    poi_output_dir = OUTPUT_DIR / poi_type
    poi_output_dir.mkdir(parents=True, exist_ok=True)
    return poi_output_dir


def sanitize_filename(name):
    """Sanitize filename while keeping Turkish characters"""
    name = name.replace('/', '-')
    return name.strip()
