#!/usr/bin/env python3
"""
Enrich Cafe & Restaurant OSM Data with Spatial Database

Enriches extracted POI data with detailed address information from a spatial database.
Adds province, city, district, street, and formatted address information.
"""

from cafe_restaurant_config import POI_OUTPUT_DIR
from database_enrichment import enrich_directory, enrich_csv_file
import sys
from pathlib import Path
import logging

# Add shared modules to path - adjust for running from datahub directory
shared_path = Path(__file__).resolve().parent.parent / "shared"
sys.path.insert(0, str(shared_path))


# Import local config
sys.path.insert(0, str(Path(__file__).resolve().parent))

# Import local config

# Set up logging
logging.basicConfig(level=logging.INFO,
                    format='%(asctime)s %(levelname)s %(message)s')
logger = logging.getLogger("cafe_restaurant_enrichment")


def main():
    """Main enrichment function"""
    logger.info(
        "Starting cafe & restaurant POI enrichment with spatial database...")

    if not POI_OUTPUT_DIR.exists():
        logger.error(f"Output directory does not exist: {POI_OUTPUT_DIR}")
        return 1

    # Check if merged file exists and enrich it
    merged_file = POI_OUTPUT_DIR / 'all_cities_cleaned.csv'
    if merged_file.exists():
        logger.info("Enriching merged dataset...")
        success = enrich_csv_file(merged_file)
        if not success:
            logger.error("Failed to enrich merged dataset")
            return 1

    # Enrich all city CSV files
    logger.info("Enriching individual city CSV files...")
    success = enrich_directory(POI_OUTPUT_DIR)

    if success:
        logger.info("Cafe & restaurant POI enrichment completed successfully!")
        return 0
    else:
        logger.error("Cafe & restaurant POI enrichment failed!")
        return 1


if __name__ == "__main__":
    sys.exit(main())
