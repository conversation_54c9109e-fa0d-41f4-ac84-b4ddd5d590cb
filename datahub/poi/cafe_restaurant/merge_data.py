#!/usr/bin/env python3
"""
Merge Cafe & Restaurant POI Data

Simple merging script that combines all cleaned city CSV files into a single dataset.
This script only merges - no cleaning is performed here.
All cleaning should be done first with clean_data.py
"""


from cafe_restaurant_config import POI_OUTPUT_DIR
import os
import glob
import pandas as pd
import sys
from pathlib import Path
import logging
from tqdm import tqdm

# Add shared modules to path - adjust for running from datahub directory
shared_path = Path(__file__).resolve().parent.parent / "shared"
sys.path.insert(0, str(shared_path))

# Import local config
sys.path.insert(0, str(Path(__file__).resolve().parent))

# Import local config

# Set up logging
logging.basicConfig(level=logging.INFO,
                    format='%(asctime)s %(levelname)s %(message)s')
logger = logging.getLogger("cafe_restaurant_merger")

# Output file for merged data
OUTPUT_FILE = POI_OUTPUT_DIR / 'all_cities_merged.csv'


def merge_all_csvs():
    """Merge all cleaned city CSV files into a single dataset"""
    logger.info("Starting merge of all cafe & restaurant CSV files...")

    # Find all CSV files in city directories
    csv_pattern = str(POI_OUTPUT_DIR / "*" / "*.csv")
    csv_files = glob.glob(csv_pattern)

    if not csv_files:
        logger.error(f"No CSV files found in {POI_OUTPUT_DIR}")
        return False

    logger.info(f"Found {len(csv_files)} CSV files to merge")

    all_dataframes = []
    total_records = 0

    # Process files with progress bar
    with tqdm(csv_files, desc="Loading CSV files", unit="file") as pbar:
        for csv_file in pbar:
            pbar.set_description(f"Loading {os.path.basename(csv_file)}")
            try:
                df = pd.read_csv(csv_file)
                if len(df) > 0:
                    all_dataframes.append(df)
                    total_records += len(df)
                    logger.info(
                        f"Loaded {len(df)} records from {os.path.basename(csv_file)}")
                else:
                    logger.info(
                        f"Skipping empty file: {os.path.basename(csv_file)}")
            except Exception as e:
                logger.error(f"Error reading {csv_file}: {e}")

    if not all_dataframes:
        logger.error("No valid dataframes to merge")
        return False

    logger.info(
        f"Merging {total_records} total records from {len(all_dataframes)} files...")

    # Merge all dataframes (no cleaning - files should already be cleaned)
    merged_df = pd.concat(all_dataframes, ignore_index=True)

    final_count = len(merged_df)
    logger.info(f"Merged dataset: {final_count} records")

    # Save merged dataset
    try:
        merged_df.to_csv(OUTPUT_FILE, index=False, encoding='utf-8')
        logger.info(f"Merged dataset saved to: {OUTPUT_FILE}")

        # Print summary statistics
        print("\n" + "="*50)
        print("MERGE SUMMARY")
        print("="*50)
        print(f"Total input files: {len(csv_files)}")
        print(f"Total input records: {total_records}")
        print(f"Final merged records: {final_count}")
        print(f"Output file: {OUTPUT_FILE}")

        # Subcategory breakdown
        if 'subcategory' in merged_df.columns:
            print("\nSubcategory breakdown:")
            subcategory_counts = merged_df['subcategory'].value_counts()
            for subcategory, count in subcategory_counts.items():
                print(f"  {subcategory}: {count}")

        print("="*50)

        return True

    except Exception as e:
        logger.error(f"Error saving merged dataset: {e}")
        return False


def main():
    """Main merge function - only merges, no cleaning"""
    logger.info("Starting cafe & restaurant data merge...")
    logger.info(
        "Note: This script only merges files. Cleaning should be done first with clean_data.py")

    if not POI_OUTPUT_DIR.exists():
        logger.error(f"Output directory does not exist: {POI_OUTPUT_DIR}")
        return 1

    success = merge_all_csvs()

    if success:
        logger.info("Cafe & restaurant data merge completed successfully!")
        return 0
    else:
        logger.error("Cafe & restaurant data merge failed!")
        return 1


if __name__ == "__main__":
    sys.exit(main())
