<!-- @format -->

# Cafe & Restaurant POI Extraction System

This system extracts cafe and restaurant Point of Interest (POI) data from OpenStreetMap for the entire country of Turkey, organizing them by city and saving them as clean, structured CSV files. This is part of the new modular POI extraction framework.

## Overview

The system follows a structured pipeline approach with clear separation of concerns:

1. **Find Data**: Download Turkey OSM data
2. **Extract Data**: Extract cafe and restaurant POIs by city
3. **Clean Data**: Validate, clean, deduplicate, and standardize extracted data
4. **Merge Data**: Combine all cleaned city data into a comprehensive dataset (merge only)
5. **Enrich Data**: Optionally enhance with spatial database information

## Features

- **Complete Coverage**: Extracts ALL cafe and restaurant POIs from Turkey
- **City-based Organization**: Organizes data by Turkish cities
- **Fast Processing**: Uses Overpass API for efficient OSM data processing
- **Clean Data**: Structured format with all required fields
- **Multilingual Support**: Extracts names in multiple languages
- **No API Limits**: Works with downloaded data, no rate limiting
- **Database Enrichment**: Optional enhancement with spatial database for detailed addresses
- **Modular Design**: Shared components for easy extension to other POI types

## File Structure

```
datahub/poi/cafe_restaurant/
├── README.md                           # This file
├── config.py                           # Cafe/restaurant specific configuration
├── extract_osm_by_city.py              # Main extraction script by city
├── clean_data.py                       # Cleans, validates, deduplicates data (clean only)
├── merge_data.py                       # Merges all cleaned data (merge only)
├── enrich_osm_with_spatialdb.py        # Database enrichment script
└── data/
    └── output/
        └── cafe_restaurant/
            ├── Adana/                  # City directories with CSV files
            │   ├── amenity_cafe.csv
            │   ├── amenity_restaurant.csv
            │   ├── shop_cafe.csv
            │   └── shop_restaurant.csv
            ├── Ankara/
            ├── İstanbul/
            ├── ... (all Turkish cities)
            └── all_cities_merged.csv   # Final merged dataset
```

## Shared Components

The system uses shared components located in `datahub/poi/shared/`:

- **config.py**: Common configuration (cities, columns, database settings)
- **utils.py**: Utility functions (validation, API requests, data cleaning)
- **download_osm_data.py**: OSM data downloader
- **database_enrichment.py**: Spatial database enrichment functionality

## Required Fields

Each POI record contains:

- `latitude, longitude` - Geographic coordinates
- `phone_number` - Contact phone number
- `description` - Additional information
- `opening_hours` - Operating hours
- `name` - Primary name
- `name_en, name_uk, name_de, name_tr, name_ru, name_ar` - Multilingual names
- `category` - Always "Food & Drink"
- `subcategory` - Specific type (Restaurant, Cafe)
- `province` - Administrative province (from database enrichment)
- `city` - Administrative city/district (from database enrichment)
- `district` - Administrative neighborhood/mahalle (from database enrichment)
- `street` - Nearest named street (from database enrichment)
- `branch, cuisine, amenity, address` - Additional details
- `full_address` - Complete formatted address (from database enrichment)

## Categories Extracted

Currently, the system extracts:

- **Restaurants** (`amenity=restaurant`, `shop=restaurant`)
- **Cafes** (`amenity=cafe`, `shop=cafe`)

## Database Requirements (for enrichment)

To use the enrichment feature, you need:

- PostgreSQL with PostGIS extension
- Spatial database with admin boundaries and road network
- Database credentials in `.env` file

Create a `.env` file in the root of datahub:

```
DB_NAME=wizlop_db
DB_USER=wizlop_user
DB_PASSWORD=wizlop_pass
DB_HOST=localhost
DB_PORT=5432
```

## Usage

### Quick Start (Recommended)

1. **Install Dependencies**:

   ```bash
   cd datahub
   pip install -r requirements.txt
   ```

2. **Run Complete Pipeline** (from datahub directory):
   ```bash
   python run_cafe_restaurant_pipeline.py
   ```

This will automatically:

- Download Turkey OSM data
- Extract all cafe and restaurant POIs
- Clean and validate data
- Merge into final dataset
- Optionally enrich with spatial database

### Individual Steps

If you want to run steps individually:

1. **Download Turkey OSM Data**:

   ```bash
   python -c "import sys; sys.path.append('../shared'); from download_osm_data import main; main()"
   ```

2. **Extract Cafe and Restaurant POIs by City**:

   ```bash
   python extract_osm_by_city.py
   ```

3. **Clean and Process Data** (validation, deduplication, standardization, country info):

   ```bash
   python clean_data.py
   ```

4. **Merge All Cleaned Data** (merge only, no cleaning):

   ```bash
   python merge_data.py
   ```

5. **Enrich with Spatial Database** (optional):
   ```bash
   python enrich_osm_with_spatialdb.py
   ```

## Performance

- **Download Time**: ~5-10 minutes (depending on internet speed)
- **Processing Time**: ~15-30 minutes for entire Turkey
- **Enrichment Time**: ~10-20 minutes (with database)
- **Output Size**: ~50-100MB of structured CSV data
- **POI Count**: ~10,000-50,000 cafe and restaurant establishments
- **City Coverage**: All 81 Turkish provinces

## Data Quality

- Removes duplicates based on coordinates
- Validates geographic coordinates
- Cleans and standardizes phone numbers
- Normalizes opening hours format
- Handles missing data gracefully
- Preserves multilingual content
- Enhanced with accurate administrative boundaries
- Includes nearest street information from road network
- Organized by city for easy access and processing

## Extending to Other POI Types

This modular system can be easily extended to other POI types:

1. Create a new directory under `datahub/poi/` (e.g., `hotels`, `gas_stations`)
2. Copy the structure from `cafe_restaurant/`
3. Modify `config.py` to define the specific amenities/shops for your POI type
4. Update the scripts as needed for your specific requirements
5. The shared components will handle common functionality

## Output

The final output is a comprehensive CSV file (`all_cities_merged.csv`) containing all cafe and restaurant POIs in Turkey with:

- Complete geographic information
- Multilingual names
- Contact details
- Administrative boundaries (if enriched)
- Nearest street information (if enriched)
- Formatted addresses (if enriched)

Ready for use in applications, analysis, or further processing!
