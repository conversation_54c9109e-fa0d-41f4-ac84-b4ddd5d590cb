#!/usr/bin/env python3
"""
OSM Cafe & Restaurant Extractor by City

Extracts cafe and restaurant POIs from OpenStreetMap data for Turkish cities.
This is part of the new modular POI extraction system.
"""

from config import COMMON_COLUMNS, OVERPASS_URL, sanitize_filename, get_turkish_cities_from_osm
from cafe_restaurant_config import get_amenities, get_subcategory, POI_OUTPUT_DIR, CATEGORY
from utils import exponential_backoff, make_overpass_request, build_overpass_query
import requests
import pandas as pd
import os
import json
import time
import logging
import sys
from pathlib import Path
from tqdm import tqdm

# Add shared modules to path - adjust for running from datahub directory
shared_path = Path(__file__).resolve().parent.parent / "shared"
sys.path.insert(0, str(shared_path))


# Import local config after shared config
sys.path.insert(0, str(Path(__file__).resolve().parent))

# Import shared config
# Import local config

# Set up logging
logging.basicConfig(level=logging.INFO,
                    format='%(asctime)s %(levelname)s %(message)s')
logger = logging.getLogger("cafe_restaurant_extractor")


def extract_tags(element, key, value, city):
    """Extract relevant tags from OSM element"""
    tags = element.get('tags', {})

    # Handle center coordinates for ways and relations
    if 'lat' in element and 'lon' in element:
        lat, lon = element['lat'], element['lon']
    elif 'center' in element:
        lat, lon = element['center']['lat'], element['center']['lon']
    else:
        lat, lon = '', ''

    return {
        'latitude': lat,
        'longitude': lon,
        'city': tags.get('addr:city', city),
        'district': tags.get('addr:district', ''),
        'street': tags.get('addr:street', ''),
        'phone_number': tags.get('phone', tags.get('contact:phone', '')),
        'description': tags.get('description', ''),
        'opening_hours': tags.get('opening_hours', ''),
        'amenity': tags.get('amenity', value if key == 'amenity' else ''),
        'cuisine': tags.get('cuisine', ''),
        'name': tags.get('name', ''),
        'name_en': tags.get('name:en', ''),
        'name_uk': tags.get('name:uk', ''),
        'name_de': tags.get('name:de', ''),
        'name_tr': tags.get('name:tr', ''),
        'name_ru': tags.get('name:ru', ''),
        'name_ar': tags.get('name:ar', ''),
        'category': CATEGORY,
        'subcategory': get_subcategory(key, value),
    }


def save_to_csv(city, key, value, rows):
    """Save extracted data to CSV file"""
    city_dir = POI_OUTPUT_DIR / sanitize_filename(city)
    city_dir.mkdir(parents=True, exist_ok=True)
    filename = city_dir / f"{key}_{value}.csv"

    df = pd.DataFrame(rows, columns=COMMON_COLUMNS)
    df.to_csv(filename, index=False, encoding='utf-8')
    logger.info(f"Saved {len(rows)} rows to {filename}")


def already_done(city, key, value):
    """Check if extraction for this city/key/value combination is already done"""
    city_dir = POI_OUTPUT_DIR / sanitize_filename(city)
    filename = city_dir / f"{key}_{value}.csv"
    return filename.exists()


def extract_city_pois(city, key, value, max_retries=3):
    """Extract POIs for a specific city and amenity/shop type"""
    if already_done(city, key, value):
        logger.info(f"Skipping {city} {key}={value} (already done)")
        return True

    query = build_overpass_query(city, key, value)
    logger.info(f"Querying {city} {key}={value}")

    data = make_overpass_request(query, max_retries)
    if data is None:
        logger.error(f"Failed to get data for {city} {key}={value}")
        return False

    elements = data.get('elements', [])
    rows = [extract_tags(e, key, value, city) for e in elements]
    save_to_csv(city, key, value, rows)

    # Small delay to be respectful to the API
    time.sleep(1)
    return True


def main():
    """Main extraction function"""
    logger.info("Starting cafe & restaurant POI extraction by city...")

    # Get cities dynamically from OSM data
    logger.info("Extracting Turkish cities from OSM data...")
    try:
        turkish_cities = get_turkish_cities_from_osm()
        logger.info(f"Found {len(turkish_cities)} cities to process")
    except Exception as e:
        logger.error(f"Failed to extract cities: {e}")
        return False

    amenity_map = get_amenities()

    # Create list of all combinations for progress tracking
    all_combinations = []
    for city in turkish_cities:
        for key, values in amenity_map.items():
            for value in values:
                all_combinations.append((city, key, value))

    logger.info(f"Total combinations to process: {len(all_combinations)}")

    # Process with progress bar
    with tqdm(total=len(all_combinations), desc="Extracting POIs", unit="combination") as pbar:
        for city, key, value in all_combinations:
            pbar.set_description(f"Processing {city} ({key}={value})")

            success = extract_city_pois(city, key, value)
            if not success:
                logger.warning(f"Failed to extract {city} {key}={value}")
                # Continue with next combination instead of stopping

            pbar.update(1)

    logger.info("Cafe & restaurant POI extraction completed!")


if __name__ == "__main__":
    main()
