<!-- @format -->

# Quick Start Guide - Cafe & Restaurant POI Extraction

## 🚀 Get Started in 2 Steps

### Step 1: Install Dependencies

```bash
cd datahub
pip install -r requirements.txt
```

### Step 2: Run the Full Pipeline

```bash
python run_cafe_restaurant_pipeline.py
```

That's it! The pipeline will automatically:

1. Download Turkey OSM data (~200MB)
2. Extract all cafe and restaurant POIs
3. Clean and validate the data
4. Merge everything into a final dataset
5. Optionally enrich with spatial database

## 📊 Expected Results

The pipeline will create:

- **Raw OSM data**: `../../../data/raw/turkey-latest.osm.pbf` (~200MB)
- **City CSV files**: `../../../data/output/cafe_restaurant/[CityName]/` (separate files by type)
- **Final dataset**: `../../../data/output/cafe_restaurant/all_cities_merged.csv` (complete dataset)

### What You'll Get:

- **Total POIs**: ~10,000-50,000 cafe and restaurant establishments
- **Subcategories**: Restaurant, Cafe
- **Coverage**: Entire Turkey (all 81 provinces)
- **Data Quality**: High completeness with phone numbers, opening hours, multilingual names

## 🔧 Individual Scripts (Advanced)

If you want to run steps individually:

```bash
# From the datahub directory:

# 1. Download Turkey OSM data (shared component)
cd datahub
python -c "import sys; sys.path.insert(0, 'poi/shared'); from download_osm_data import main; main()"

# 2. Extract cafe & restaurant POIs by city
python -c "import sys; sys.path.insert(0, 'poi/shared'); sys.path.insert(0, 'poi/cafe_restaurant'); from extract_osm_by_city import main; main()"

# 3. Clean, validate, and process data (includes deduplication, standardization)
python -c "import sys; sys.path.insert(0, 'poi/shared'); sys.path.insert(0, 'poi/cafe_restaurant'); from clean_data import main; main()"

# 4. Merge all cleaned data into final dataset (merge only)
python -c "import sys; sys.path.insert(0, 'poi/shared'); sys.path.insert(0, 'poi/cafe_restaurant'); from merge_data import main; main()"

# 5. Enrich with spatial database (optional)
python -c "import sys; sys.path.insert(0, 'poi/shared'); sys.path.insert(0, 'poi/cafe_restaurant'); from enrich_osm_with_spatialdb import main; main()"
```

## 📋 Data Fields

Each POI record contains:

- `latitude, longitude` - Geographic coordinates
- `phone_number` - Contact phone number
- `description` - Additional information
- `opening_hours` - Operating hours
- `name` - Primary name
- `name_en, name_uk, name_de, name_tr, name_ru, name_ar` - Multilingual names
- `category` - Always "Food & Drink"
- `subcategory` - Specific type (Restaurant, Cafe)
- `city, district, street, branch, cuisine, amenity, address` - Additional details

### With Database Enrichment:

- `province` - Administrative province
- `city_admin` - Administrative city/district
- `district` - Administrative neighborhood/mahalle
- `street` - Nearest named street
- `full_address` - Complete formatted address

## ⚡ Performance

- **Download Time**: ~5-10 minutes
- **Processing Time**: ~15-30 minutes
- **Enrichment Time**: ~10-20 minutes (optional)
- **Total Time**: ~20-60 minutes
- **Disk Space**: ~500MB required

## 🗂️ New Modular Structure

This system uses a new modular approach:

```
datahub/poi/
├── shared/                    # Common components for all POI types
│   ├── config.py             # Shared configuration
│   ├── utils.py              # Utility functions
│   ├── download_osm_data.py  # OSM data downloader
│   └── database_enrichment.py # Spatial database enrichment
└── cafe_restaurant/          # Cafe & restaurant specific
    ├── config.py             # Specific configuration
    ├── extract_osm_by_city.py # Extraction script
    ├── clean_osm_city_csvs.py # Cleaning script
    ├── clean_and_merge_all.py # Merging script
    └── enrich_osm_with_spatialdb.py # Enrichment script
```

## 🔧 Database Setup (Optional)

For spatial database enrichment, create a `.env` file in `datahub/`:

```
DB_NAME=wizlop_db
DB_USER=wizlop_user
DB_PASSWORD=wizlop_pass
DB_HOST=localhost
DB_PORT=5432
```

## 🆘 Troubleshooting

### Common Issues:

1. **Missing dependencies**: Run `pip install -r ../../../requirements.txt`
2. **Disk space**: Ensure you have at least 500MB free space
3. **Internet connection**: Required for OSM download
4. **Memory issues**: The system processes data in batches to handle large datasets
5. **Database connection**: Enrichment will skip if database is not available

### Get Help:

- Check the detailed README.md for comprehensive documentation
- Review error messages in the console output
- Run individual scripts to isolate issues

## 🎯 What You'll Get

A comprehensive, clean, and organized dataset of all cafe and restaurant establishments in Turkey, ready for use in your applications!

The final CSV file will be located at:
`../../../data/output/cafe_restaurant/all_cities_merged.csv`

## 🚀 Extending to Other POI Types

This modular system makes it easy to add other POI types:

1. Copy the `cafe_restaurant/` directory structure
2. Modify the configuration for your POI type
3. The shared components handle all the common functionality

Perfect for building a comprehensive POI database for Turkey!
