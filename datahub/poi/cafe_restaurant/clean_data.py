#!/usr/bin/env python3
"""
Clean Cafe & Restaurant POI Data

Comprehensive cleaning script that handles:
- Basic validation (names, coordinates)
- Data standardization
- Duplicate removal
- Country information addition
- Phone number cleaning

This script only cleans data - no merging is performed here.
"""

from cafe_restaurant_config import POI_OUTPUT_DIR
from config import NAME_FIELDS
from utils import has_any_name, has_lat_lon, validate_poi_data, get_country_from_coordinates
import pandas as pd
import os
import sys
from pathlib import Path
import logging
from tqdm import tqdm

# Add shared modules to path - adjust for running from datahub directory
shared_path = Path(__file__).resolve().parent.parent / "shared"
sys.path.insert(0, str(shared_path))


# Import local config
sys.path.insert(0, str(Path(__file__).resolve().parent))

# Import shared config
# Import local config

# Set up logging
logging.basicConfig(level=logging.INFO,
                    format='%(asctime)s %(levelname)s %(message)s')
logger = logging.getLogger("cafe_restaurant_cleaner")


def add_country_info(df):
    """Add country information to dataframe"""
    logger.info("Adding country information...")

    if 'country' not in df.columns:
        df['country'] = ''

    with tqdm(total=len(df), desc="Adding country info", unit="record") as pbar:
        for idx, row in df.iterrows():
            try:
                lat, lon = float(row['latitude']), float(row['longitude'])
                country = get_country_from_coordinates(lat, lon)
                df.at[idx, 'country'] = country

            except Exception as e:
                logger.warning(f"Error getting country for record {idx}: {e}")
                df.at[idx, 'country'] = 'Unknown'

            pbar.update(1)

    return df


def remove_duplicates(df):
    """Remove duplicate POIs based on coordinates and names"""
    logger.info(f"Removing duplicates from {len(df)} records...")

    # Remove exact coordinate duplicates
    df_dedup = df.drop_duplicates(subset=['latitude', 'longitude'])

    # Remove near-duplicates (within ~10 meters) with same name
    # This is a simplified approach - for more sophisticated deduplication,
    # we could use spatial clustering

    logger.info(f"After deduplication: {len(df_dedup)} records remain")
    return df_dedup


def standardize_data(df):
    """Standardize data formats and clean up inconsistencies"""
    logger.info("Standardizing data formats...")

    # Ensure category is consistent
    df['category'] = 'Food & Drink'

    # Clean up subcategory values
    df['subcategory'] = df['subcategory'].str.title()

    # Drop amenity column as it's been replaced by category and subcategory
    if 'amenity' in df.columns:
        df = df.drop(columns=['amenity'])
        logger.info(
            "Dropped amenity column (replaced by category and subcategory)")

    # Standardize empty values
    for col in df.columns:
        if col in NAME_FIELDS or col in ['phone_number', 'opening_hours', 'description']:
            df[col] = df[col].fillna('').astype(str)
            df[col] = df[col].replace('nan', '')

    return df


def clean_csv_file(file_path):
    """Clean a single CSV file with comprehensive cleaning"""
    if not file_path.exists():
        logger.warning(f"File does not exist: {file_path}")
        return

    if os.path.getsize(file_path) == 0:
        logger.info(f"Skipping empty file (0 bytes): {file_path}")
        return

    try:
        df = pd.read_csv(file_path)
    except pd.errors.EmptyDataError:
        logger.info(f"Skipping empty file (no columns): {file_path}")
        return
    except Exception as e:
        logger.error(f"Error reading {file_path}: {e}")
        return

    if len(df) == 0:
        logger.info(f"Skipping empty dataframe: {file_path}")
        return

    before_count = len(df)

    # Step 1: Basic validation (remove entries without names or coordinates)
    df_clean = validate_poi_data(df)

    # Step 2: Remove duplicates
    df_clean = remove_duplicates(df_clean)

    # Step 3: Standardize data formats
    df_clean = standardize_data(df_clean)

    # Step 4: Add country information
    df_clean = add_country_info(df_clean)

    after_count = len(df_clean)

    # Save cleaned data
    try:
        df_clean.to_csv(file_path, index=False, encoding='utf-8')
        logger.info(
            f"{os.path.relpath(file_path, POI_OUTPUT_DIR)}: {before_count} -> {after_count} rows kept")
    except Exception as e:
        logger.error(f"Error saving cleaned file {file_path}: {e}")


def clean_city_directory(city_dir):
    """Clean all CSV files in a city directory"""
    if not city_dir.is_dir():
        logger.warning(f"Not a directory: {city_dir}")
        return

    csv_files = list(city_dir.glob('*.csv'))
    if not csv_files:
        logger.info(f"No CSV files found in {city_dir}")
        return

    logger.info(f"Cleaning {len(csv_files)} CSV files in {city_dir.name}")

    for csv_file in csv_files:
        clean_csv_file(csv_file)


def main():
    """Main cleaning function"""
    logger.info("Starting cafe & restaurant data cleaning...")
    logger.info("This script only cleans data - no merging is performed")

    if not POI_OUTPUT_DIR.exists():
        logger.error(f"Output directory does not exist: {POI_OUTPUT_DIR}")
        return 1

    # Get all city directories
    city_dirs = [d for d in POI_OUTPUT_DIR.iterdir() if d.is_dir()]

    if not city_dirs:
        logger.warning(f"No city directories found in {POI_OUTPUT_DIR}")
        return 1

    logger.info(f"Found {len(city_dirs)} city directories to process")

    # Process with progress bar
    with tqdm(city_dirs, desc="Cleaning city directories", unit="city") as pbar:
        for city_dir in pbar:
            pbar.set_description(f"Cleaning {city_dir.name}")
            clean_city_directory(city_dir)

    logger.info("Cafe & restaurant data cleaning completed!")
    return 0


if __name__ == "__main__":
    sys.exit(main())
