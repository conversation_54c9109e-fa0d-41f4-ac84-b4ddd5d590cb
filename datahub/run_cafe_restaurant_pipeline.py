#!/usr/bin/env python3
"""
Wrapper script to run the cafe & restaurant POI extraction pipeline from datahub directory.
This script sets up the correct paths and then runs the pipeline.
"""

import sys
import subprocess
import logging
import os
import importlib
from pathlib import Path

# Set up paths
shared_path = Path(__file__).resolve().parent / "poi" / "shared"
local_path = Path(__file__).resolve().parent / "poi" / "cafe_restaurant"

sys.path.insert(0, str(shared_path))
sys.path.insert(0, str(local_path))

# Dynamic import after path setup
download_osm_module = importlib.import_module('download_osm_data')
download_turkey_osm = download_osm_module.download_turkey_osm

# Import shared database enrichment
database_enrichment_module = importlib.import_module('database_enrichment')
enrich_csv_file = database_enrichment_module.enrich_csv_file

# Set up logging
logging.basicConfig(level=logging.INFO,
                    format='%(asctime)s %(levelname)s %(message)s')
logger = logging.getLogger("cafe_restaurant_pipeline")


def run_script(script_name, description):
    """Run a Python script and return success status"""
    logger.info(f"Starting: {description}")

    script_path = local_path / script_name
    if not script_path.exists():
        logger.error(f"Script not found: {script_path}")
        return False

    try:
        # Set up environment for subprocess
        env = dict(os.environ)
        env['PYTHONPATH'] = f"{shared_path}:{local_path}:{env.get('PYTHONPATH', '')}"

        result = subprocess.run([sys.executable, str(script_path)],
                                capture_output=True, text=True, timeout=3600, env=env)

        if result.returncode == 0:
            logger.info(f"Completed successfully: {description}")
            if result.stdout:
                logger.info(f"Output: {result.stdout}")
            return True
        else:
            logger.error(f"Failed: {description}")
            if result.stderr:
                logger.error(f"Error: {result.stderr}")
            if result.stdout:
                logger.info(f"Output: {result.stdout}")
            return False

    except subprocess.TimeoutExpired:
        logger.error(f"Timeout: {description}")
        return False
    except Exception as e:
        logger.error(f"Exception running {description}: {e}")
        return False


def main():
    """Main pipeline function"""

    logger.info("="*60)
    logger.info("STARTING CAFE & RESTAURANT POI EXTRACTION PIPELINE")
    logger.info("="*60)

    # Step 1: Download OSM data
    logger.info("Step 1: Downloading Turkey OSM data...")
    if not download_turkey_osm():
        logger.error("Failed to download OSM data")
        return 1

    # Step 2: Extract POIs by city
    if not run_script("extract_osm_by_city.py", "Step 2: Extract POIs by city"):
        logger.error("POI extraction failed")
        return 1

    # Step 3: Clean data
    if not run_script("clean_data.py", "Step 3: Clean and process data"):
        logger.error("Data cleaning failed")
        return 1

    # Step 4: Merge all cleaned data
    if not run_script("merge_data.py", "Step 4: Merge all cleaned data"):
        logger.error("Data merging failed")
        return 1

    # Step 5: Enrich with spatial database (optional)
    logger.info("Step 5: Enriching with spatial database (optional)...")
    try:
        # Import config after path setup
        from cafe_restaurant_config import POI_OUTPUT_DIR

        # Check if merged file exists and enrich it
        merged_file = POI_OUTPUT_DIR / 'all_cities_merged.csv'
        if merged_file.exists():
            logger.info(
                "Enriching merged dataset with shared database enrichment...")
            enrich_success = enrich_csv_file(merged_file)
            if enrich_success:
                logger.info(
                    "Spatial database enrichment completed successfully")
            else:
                logger.warning(
                    "Spatial database enrichment failed - this is optional and pipeline can continue")
        else:
            logger.warning(
                f"Merged file not found: {merged_file} - skipping enrichment")
    except Exception as e:
        logger.warning(
            f"Spatial database enrichment failed: {e} - this is optional and pipeline can continue")

    logger.info("="*60)
    logger.info("CAFE & RESTAURANT POI EXTRACTION PIPELINE COMPLETED!")
    logger.info("="*60)

    # Print summary
    from cafe_restaurant_config import POI_OUTPUT_DIR
    merged_file = POI_OUTPUT_DIR / 'all_cities_merged.csv'

    if merged_file.exists():
        import pandas as pd
        try:
            df = pd.read_csv(merged_file)
            logger.info(f"Final dataset contains {len(df)} POI records")
            logger.info(f"Output file: {merged_file}")

            if 'subcategory' in df.columns:
                subcategory_counts = df['subcategory'].value_counts()
                logger.info("Subcategory breakdown:")
                for subcategory, count in subcategory_counts.items():
                    logger.info(f"  {subcategory}: {count}")
        except Exception as e:
            logger.warning(f"Could not read final dataset for summary: {e}")

    return 0


if __name__ == "__main__":
    sys.exit(main())
