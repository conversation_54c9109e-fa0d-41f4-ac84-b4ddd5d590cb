<!-- @format -->

# Wizlop DatabaseHub: Setup & Modular Schema Guide

## 1. Environment Setup

```sh
cd databaseHub
python3 -m venv venv
source venv/bin/activate
pip install -r requirements.txt
```

## 2. Configure Database Credentials

Create or edit `.env` in `databaseHub/`:

```
DB_NAME=wizlop_db
DB_USER=wizlop_user
DB_PASSWORD=wizlop_pass
DB_HOST=localhost
DB_PORT=5432
```

## 3. Create PostgreSQL Database & User

Run the portable setup script:

```sh
python scripts/spatial/setup/setup_portable.py
```

- This script creates the database and user, and enables required extensions (PostGIS, uuid-ossp).
- **It does NOT create schemas or tables.**

## 4. Run Schema Migrations (Order)

Run these SQL files in order using `psql`:

```sh
psql -U wizlop_user -h localhost -d wizlop_db -f schema/00_create_extensions_and_schemas.sql
psql -U wizlop_user -h localhost -d wizlop_db -f schema/backend_schema.sql
psql -U wizlop_user -h localhost -d wizlop_db -f schema/spatial_schema.sql
psql -U wizlop_user -h localhost -d wizlop_db -f schema/cross_schema.sql
psql -U wizlop_user -h localhost -d wizlop_db -f schema/top_location.sql

```

- All user references are now UUIDs for consistency.
- Permissions and roles are unified in the backend schema.
- Triggers, indexes, and functions are now included in the main schema files for maintainability.

## 5. Import Data

- **Download OSM Data:**
  ```sh
  python scripts/spatial/setup/download_osm_data.py
  ```
- **Extract & Import Admin Boundaries:**
  ```sh
  python scripts/spatial/admin_boundaries/extraction/extract_admin_boundaries.py
  python scripts/spatial/admin_boundaries/importing/import_admin_boundaries.py
  ```
- **Extract & Import Road Network:**
  ```sh
  python scripts/spatial/road_network/extraction/extract_road_network.py
  python scripts/spatial/road_network/importing/import_roads.py
  ```
- **Import POIs (optional):**
  ```sh
  python scripts/spatial/pois/importing/import_pois.py
  ```
- **Optimize Database (optional):**
  ```sh
  python scripts/spatial/setup/optimize_spatial_db.py
  ```

## 6. Schema File Overview

- `00_create_extensions_and_schemas.sql`: Extensions and schema creation
- `backend_schema.sql`: Backend tables, triggers, indexes, and functions (users, auth, reviews, etc)
- `spatial_schema.sql`: Spatial tables, triggers, indexes, and functions (POIs, boundaries, etc)
- `cross_schema.sql`: Cross-schema views, triggers, and logic

## 7. Key Relationships & Usage

- Users (`backend_schema.nextauth_users`) are referenced by UUID in all tables.
- POIs (`spatial_schema.pois`, `user_pois_temp`, `user_pois_approved`) are the core spatial entities.
- Media (`spatial_schema.poi_media`) is linked to POIs and users.
- Reviews (`backend_schema.user_location_reviews`, `spatial_schema.community_reviews`) are linked to both users and POIs.
- All media files should be stored in object storage (e.g., S3); only URLs are stored in the DB.

## 8. Example SQL Operations

- **Insert a POI:**
  ```sql
  INSERT INTO spatial_schema.pois (name, category, city, latitude, longitude, status)
  VALUES ('Galata Tower', 'Historical', 'Istanbul', 41.0256, 28.9744, 'active');
  ```
- **Add media to a POI:**
  ```sql
  INSERT INTO spatial_schema.poi_media (poi_id, user_id, url, media_type, caption)
  VALUES (1, 'user-uuid', 'https://cdn.example.com/galata.jpg', 'image', 'View from the top');
  ```
- **Add a review:**
  ```sql
  INSERT INTO backend_schema.user_location_reviews (user_id, poi_id, poi_type, rating, review_text)
  VALUES ('user-uuid', 1, 'official', 5, 'Amazing view!');
  ```
- **Like a media item:**
  ```sql
  INSERT INTO spatial_schema.poi_media_likes (media_id, user_id)
  VALUES (10, 'user-uuid');
  ```

## 9. Best Practices

- Always use UUIDs for user references.
- Store all media in object storage, not in the DB.
- Use provided views and triggers for efficient querying and automatic metric updates.
- Review the schema files for detailed comments and advanced features.

---

For more details, see the schema files in `databaseHub/schema/` and comments within each SQL file.
