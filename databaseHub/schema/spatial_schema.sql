-- spatial_schema.sql
-- Unified spatial schema for Wizlop

-- Administrative boundaries
CREATE TABLE IF NOT EXISTS spatial_schema.admin_boundaries (
  id SERIAL PRIMARY KEY,
  osm_id BIGINT,
  admin_level INTEGER,
  well_known_level INTEGER,
  place_type VA<PERSON>HA<PERSON>(50),
  name <PERSON><PERSON><PERSON><PERSON>(255),
  name_en VARCHAR(255),
  name_tr VA<PERSON>HA<PERSON>(255),
  type VA<PERSON>HA<PERSON>(50),
  boundary VARCHAR(50),
  place VARCHAR(50),
  latitude DOUBLE PRECISION,
  longitude DOUBLE PRECISION,
  geom GEOMETRY(MULTIPOLYGON, 4326) NOT NULL
);

-- Example: OSM city boundaries
CREATE TABLE IF NOT EXISTS spatial_schema.city_boundaries (
  id SERIAL PRIMARY KEY,
  name VARCHAR(255) NOT NULL,
  osm_id BIGINT,
  geom GEOMETRY(MULTIPOLYGON, 4326) NOT NULL,
  admin_level INTEGER,
  country_code VARCHAR(10),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Example: OSM POIs
CREATE TABLE IF NOT EXISTS spatial_schema.osm_pois (
  id SERIAL PRIMARY KEY,
  osm_id BIGINT,
  name VARCHAR(255),
  category VARCHAR(100),
  subcategory VARCHAR(100),
  latitude DOUBLE PRECISION,
  longitude DOUBLE PRECISION,
  geom GEOMETRY(POINT, 4326),
  country VARCHAR(100),
  tags JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- User POIs Temp Table (Pending Admin Review)
CREATE TABLE IF NOT EXISTS spatial_schema.user_pois_temp (
    id SERIAL PRIMARY KEY,
    submitted_by_user_id UUID NOT NULL REFERENCES backend_schema.nextauth_users(id) ON DELETE CASCADE,
    name TEXT,
    name_en TEXT,
    name_tr TEXT,
    name_uk TEXT,
    name_de TEXT,
    name_ru TEXT,
    name_ar TEXT,
    category VARCHAR(50),
    subcategory VARCHAR(50),
    cuisine VARCHAR(100),
    city VARCHAR(100),
    district VARCHAR(100),
    neighborhood VARCHAR(100),
    street TEXT,
    full_address TEXT,
    province TEXT,
    country VARCHAR(100),
    phone_number VARCHAR(50),
    opening_hours TEXT,
    description TEXT,
    latitude DECIMAL(10,8),
    longitude DECIMAL(11,8),
    geom GEOMETRY(POINT, 4326),
    user_rating_avg DOUBLE PRECISION,
    user_rating_count INTEGER DEFAULT 0,
    view_count INTEGER DEFAULT 0,
    favorite_count INTEGER DEFAULT 0,
    visit_count INTEGER DEFAULT 0,
    share_count INTEGER DEFAULT 0,
    like_count INTEGER DEFAULT 0,
    review_count INTEGER DEFAULT 0,
    submission_notes TEXT,
    submission_type VARCHAR(20) DEFAULT 'new_poi' CHECK (submission_type IN ('new_poi', 'info_update', 'closure_request')),
    target_poi_id INTEGER REFERENCES spatial_schema.pois(id),
    admin_review_status VARCHAR(20) DEFAULT 'pending' CHECK (admin_review_status IN ('pending', 'reviewing', 'approved', 'rejected')),
    admin_review_notes TEXT,
    reviewed_by UUID,
    reviewed_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- User POIs Approved Table (Passed Admin Review)
CREATE TABLE IF NOT EXISTS spatial_schema.user_pois_approved (
    id SERIAL PRIMARY KEY,
    original_temp_id INTEGER REFERENCES spatial_schema.user_pois_temp(id) ON DELETE SET NULL,
    submitted_by_user_id UUID NOT NULL REFERENCES backend_schema.nextauth_users(id) ON DELETE CASCADE,
    final_poi_id INTEGER REFERENCES spatial_schema.pois(id) ON DELETE SET NULL,
    approved_by_admin_id UUID REFERENCES backend_schema.nextauth_users(id) ON DELETE SET NULL,
    name TEXT,
    name_en TEXT,
    name_tr TEXT,
    name_uk TEXT,
    name_de TEXT,
    name_ru TEXT,
    name_ar TEXT,
    category VARCHAR(50),
    subcategory VARCHAR(50),
    cuisine VARCHAR(100),
    city VARCHAR(100),
    district VARCHAR(100),
    neighborhood VARCHAR(100),
    street TEXT,
    full_address TEXT,
    province TEXT,
    country VARCHAR(100),
    phone_number VARCHAR(50),
    opening_hours TEXT,
    description TEXT,
    latitude DECIMAL(10,8),
    longitude DECIMAL(11,8),
    geom GEOMETRY(POINT, 4326),
    user_rating_avg DOUBLE PRECISION,
    user_rating_count INTEGER DEFAULT 0,
    view_count INTEGER DEFAULT 0,
    favorite_count INTEGER DEFAULT 0,
    visit_count INTEGER DEFAULT 0,
    share_count INTEGER DEFAULT 0,
    like_count INTEGER DEFAULT 0,
    review_count INTEGER DEFAULT 0,
    democratic_categories JSONB DEFAULT '[]',
    community_tags JSONB DEFAULT '[]',
    legacy_categories JSONB DEFAULT '{}',
    submission_notes TEXT,
    submission_type VARCHAR(20) DEFAULT 'new_poi' CHECK (submission_type IN ('new_poi', 'info_update', 'closure_request')),
    admin_review_notes TEXT,
    approved_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Main POIs table (central POI table)
-- Note: Using INTEGER for optimal performance (current max: 21,638, INTEGER limit: 2.1B)
CREATE TABLE IF NOT EXISTS spatial_schema.pois (
  id SERIAL PRIMARY KEY,
  osm_id BIGINT,
  name VARCHAR(255),
  profile_picture_url TEXT,
  name_en TEXT,
  name_uk TEXT,
  name_de TEXT,
  name_tr TEXT,
  name_ru TEXT,
  name_ar TEXT,
  category VARCHAR(250),
  subcategory VARCHAR(250),
  cuisine VARCHAR(250),
  city VARCHAR(250),
  district VARCHAR(250),
  neighborhood VARCHAR(250),
  street TEXT,
  full_address TEXT,
  province TEXT,
  country VARCHAR(100),
  phone_number VARCHAR(250),
  opening_hours TEXT,
  description TEXT,
  democratic_categories JSONB DEFAULT '[]',
  community_tags JSONB DEFAULT '[]',
  legacy_categories JSONB DEFAULT '[]',
  online_rating_avg DOUBLE PRECISION,
  online_rating_count INTEGER DEFAULT 0,
  online_rating_source TEXT,
  online_reviews JSONB DEFAULT '[]',
  last_online_update TIMESTAMP WITH TIME ZONE,
  trending_score INTEGER DEFAULT 0,
  price_range VARCHAR(50),
  reservation_required BOOLEAN DEFAULT FALSE,
  accessibility_rating INTEGER DEFAULT 0,
  media_count INTEGER DEFAULT 0,
  last_verified_at TIMESTAMP WITH TIME ZONE,
  latitude DOUBLE PRECISION,
  longitude DOUBLE PRECISION,
  geom GEOMETRY(POINT, 4326),
  tags JSONB DEFAULT '{}',
  status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'pending', 'deleted')),
  verification_level VARCHAR(20) DEFAULT 'unverified' CHECK (verification_level IN ('unverified', 'community', 'verified', 'official')),
  popularity_score INTEGER DEFAULT 0,
  promoted_at TIMESTAMP WITH TIME ZONE,
  user_rating_avg DOUBLE PRECISION,
  user_rating_count INTEGER DEFAULT 0,
  view_count INTEGER DEFAULT 0,
  favorite_count INTEGER DEFAULT 0,
  visit_count INTEGER DEFAULT 0,
  share_count INTEGER DEFAULT 0,
  like_count INTEGER DEFAULT 0,
  review_count INTEGER DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Democratic categorization tables
CREATE TABLE IF NOT EXISTS spatial_schema.master_categories (
  id SERIAL PRIMARY KEY,
  category_name VARCHAR(100) UNIQUE NOT NULL,
  category_description TEXT,
  parent_category_id INTEGER REFERENCES spatial_schema.master_categories(id),
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE IF NOT EXISTS spatial_schema.poi_categories (
  id SERIAL PRIMARY KEY,
  poi_id INTEGER REFERENCES spatial_schema.pois(id) ON DELETE CASCADE,
  category_name VARCHAR(100) NOT NULL,
  total_votes INTEGER DEFAULT 0,
  positive_votes INTEGER DEFAULT 0,
  negative_votes INTEGER DEFAULT 0,
  confidence_score DOUBLE PRECISION DEFAULT 0.0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Individual user votes on POI categories (for democratic categorization)
CREATE TABLE IF NOT EXISTS spatial_schema.poi_category_votes (
  id SERIAL PRIMARY KEY,
  poi_id INTEGER REFERENCES spatial_schema.pois(id) ON DELETE CASCADE,
  user_id UUID REFERENCES backend_schema.nextauth_users(id) ON DELETE CASCADE,
  category_name VARCHAR(100) NOT NULL,
  vote_type VARCHAR(10) NOT NULL CHECK (vote_type IN ('positive', 'negative')),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(poi_id, user_id, category_name)
);

-- Community reviews table
CREATE TABLE IF NOT EXISTS spatial_schema.community_reviews (
  id SERIAL PRIMARY KEY,
  poi_id INTEGER REFERENCES spatial_schema.pois(id) ON DELETE CASCADE,
  user_id UUID REFERENCES backend_schema.nextauth_users(id) ON DELETE CASCADE,
  rating INTEGER CHECK (rating >= 1 AND rating <= 5),
  review_text TEXT,
  review_title VARCHAR(200),
  tags JSONB DEFAULT '[]',
  is_verified BOOLEAN DEFAULT FALSE,
  helpful_votes INTEGER DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- POI tags table
CREATE TABLE IF NOT EXISTS spatial_schema.poi_tags (
  id SERIAL PRIMARY KEY,
  poi_id INTEGER REFERENCES spatial_schema.pois(id) ON DELETE CASCADE,
  tag_name VARCHAR(50) NOT NULL,
  tag_type VARCHAR(20) DEFAULT 'user' CHECK (tag_type IN ('user', 'system', 'verified')),
  vote_count INTEGER DEFAULT 1,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Verification tasks table
CREATE TABLE IF NOT EXISTS spatial_schema.verification_tasks (
  id SERIAL PRIMARY KEY,
  poi_id INTEGER REFERENCES spatial_schema.pois(id) ON DELETE CASCADE,
  task_type VARCHAR(50) NOT NULL,
  status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'in_progress', 'completed', 'failed')),
  priority INTEGER DEFAULT 1 CHECK (priority >= 1 AND priority <= 5),
  assigned_to UUID REFERENCES backend_schema.nextauth_users(id) ON DELETE SET NULL,
  description TEXT,
  metadata JSONB DEFAULT '{}',
  expires_at TIMESTAMP WITH TIME ZONE,
  completed_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- POI media tables
CREATE TABLE IF NOT EXISTS spatial_schema.poi_media (
  id SERIAL PRIMARY KEY,
  poi_id INTEGER REFERENCES spatial_schema.pois(id) ON DELETE CASCADE,
  user_id UUID REFERENCES backend_schema.nextauth_users(id) ON DELETE SET NULL,
  media_type VARCHAR(20) NOT NULL CHECK (media_type IN ('photo', 'video', 'audio')),
  media_url TEXT NOT NULL,
  thumbnail_url TEXT,
  caption TEXT,
  metadata JSONB DEFAULT '{}',
  is_verified BOOLEAN DEFAULT FALSE,
  like_count INTEGER DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE IF NOT EXISTS spatial_schema.poi_media_likes (
  id SERIAL PRIMARY KEY,
  media_id INTEGER REFERENCES spatial_schema.poi_media(id) ON DELETE CASCADE,
  user_id UUID REFERENCES backend_schema.nextauth_users(id) ON DELETE CASCADE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(media_id, user_id)
);

CREATE TABLE IF NOT EXISTS spatial_schema.poi_media_favorites (
  id SERIAL PRIMARY KEY,
  media_id INTEGER REFERENCES spatial_schema.poi_media(id) ON DELETE CASCADE,
  user_id UUID REFERENCES backend_schema.nextauth_users(id) ON DELETE CASCADE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(media_id, user_id)
);

-- Roads table for road network data
CREATE TABLE IF NOT EXISTS spatial_schema.roads (
  id SERIAL PRIMARY KEY,
  osm_id BIGINT,
  highway VARCHAR(50),
  name VARCHAR(255),
  name_en VARCHAR(255),
  name_tr VARCHAR(255),
  oneway VARCHAR(10),
  maxspeed VARCHAR(20),
  surface VARCHAR(50),
  lanes VARCHAR(10),
  geom GEOMETRY(LINESTRING, 4326),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add other spatial tables as needed 

-- Spatial triggers
CREATE OR REPLACE FUNCTION spatial_schema.update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger function for user POI tables
CREATE OR REPLACE FUNCTION spatial_schema.update_user_poi_timestamp()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Triggers for POI workflow tables
DROP TRIGGER IF EXISTS update_user_pois_temp_updated_at ON spatial_schema.user_pois_temp;
CREATE TRIGGER update_user_pois_temp_updated_at
  BEFORE UPDATE ON spatial_schema.user_pois_temp
  FOR EACH ROW EXECUTE FUNCTION spatial_schema.update_user_poi_timestamp();

DROP TRIGGER IF EXISTS update_user_pois_approved_updated_at ON spatial_schema.user_pois_approved;
CREATE TRIGGER update_user_pois_approved_updated_at
  BEFORE UPDATE ON spatial_schema.user_pois_approved
  FOR EACH ROW EXECUTE FUNCTION spatial_schema.update_user_poi_timestamp();

-- Triggers for new tables
DROP TRIGGER IF EXISTS update_pois_updated_at ON spatial_schema.pois;
CREATE TRIGGER update_pois_updated_at
  BEFORE UPDATE ON spatial_schema.pois
  FOR EACH ROW EXECUTE FUNCTION spatial_schema.update_updated_at_column();

DROP TRIGGER IF EXISTS update_poi_categories_updated_at ON spatial_schema.poi_categories;
CREATE TRIGGER update_poi_categories_updated_at
  BEFORE UPDATE ON spatial_schema.poi_categories
  FOR EACH ROW EXECUTE FUNCTION spatial_schema.update_updated_at_column();

DROP TRIGGER IF EXISTS update_poi_category_votes_updated_at ON spatial_schema.poi_category_votes;
CREATE TRIGGER update_poi_category_votes_updated_at
  BEFORE UPDATE ON spatial_schema.poi_category_votes
  FOR EACH ROW EXECUTE FUNCTION spatial_schema.update_updated_at_column();

DROP TRIGGER IF EXISTS update_community_reviews_updated_at ON spatial_schema.community_reviews;
CREATE TRIGGER update_community_reviews_updated_at
  BEFORE UPDATE ON spatial_schema.community_reviews
  FOR EACH ROW EXECUTE FUNCTION spatial_schema.update_updated_at_column();

DROP TRIGGER IF EXISTS update_verification_tasks_updated_at ON spatial_schema.verification_tasks;
CREATE TRIGGER update_verification_tasks_updated_at
  BEFORE UPDATE ON spatial_schema.verification_tasks
  FOR EACH ROW EXECUTE FUNCTION spatial_schema.update_updated_at_column();

DROP TRIGGER IF EXISTS update_poi_media_updated_at ON spatial_schema.poi_media;
CREATE TRIGGER update_poi_media_updated_at
  BEFORE UPDATE ON spatial_schema.poi_media
  FOR EACH ROW EXECUTE FUNCTION spatial_schema.update_updated_at_column();

DROP TRIGGER IF EXISTS update_roads_updated_at ON spatial_schema.roads;
CREATE TRIGGER update_roads_updated_at
  BEFORE UPDATE ON spatial_schema.roads
  FOR EACH ROW EXECUTE FUNCTION spatial_schema.update_updated_at_column();

-- Spatial indexes
CREATE INDEX IF NOT EXISTS idx_user_pois_temp_geom ON spatial_schema.user_pois_temp USING GIST (geom);
CREATE INDEX IF NOT EXISTS idx_user_pois_approved_geom ON spatial_schema.user_pois_approved USING GIST (geom);
CREATE INDEX IF NOT EXISTS idx_user_pois_temp_submitted_by ON spatial_schema.user_pois_temp(submitted_by_user_id);
CREATE INDEX IF NOT EXISTS idx_user_pois_temp_status ON spatial_schema.user_pois_temp(admin_review_status);
CREATE INDEX IF NOT EXISTS idx_user_pois_temp_category ON spatial_schema.user_pois_temp(category);
CREATE INDEX IF NOT EXISTS idx_user_pois_approved_submitted_by ON spatial_schema.user_pois_approved(submitted_by_user_id);
CREATE INDEX IF NOT EXISTS idx_user_pois_approved_category ON spatial_schema.user_pois_approved(category);

-- Indexes for main pois table
CREATE INDEX IF NOT EXISTS idx_pois_geom ON spatial_schema.pois USING GIST (geom);
CREATE INDEX IF NOT EXISTS idx_pois_category ON spatial_schema.pois(category);
CREATE INDEX IF NOT EXISTS idx_pois_subcategory ON spatial_schema.pois(subcategory);
CREATE INDEX IF NOT EXISTS idx_pois_city ON spatial_schema.pois(city);
CREATE INDEX IF NOT EXISTS idx_pois_status ON spatial_schema.pois(status);
CREATE INDEX IF NOT EXISTS idx_pois_verification_level ON spatial_schema.pois(verification_level);
CREATE INDEX IF NOT EXISTS idx_pois_popularity_score ON spatial_schema.pois(popularity_score DESC);
CREATE INDEX IF NOT EXISTS idx_pois_rating ON spatial_schema.pois(user_rating_avg DESC NULLS LAST);
CREATE INDEX IF NOT EXISTS idx_pois_created_at ON spatial_schema.pois(created_at);

-- Composite indexes for performance
CREATE INDEX IF NOT EXISTS idx_pois_amenity_popularity ON spatial_schema.pois(category, popularity_score DESC);
CREATE INDEX IF NOT EXISTS idx_pois_rating_popularity ON spatial_schema.pois(user_rating_avg DESC NULLS LAST, popularity_score DESC);
CREATE INDEX IF NOT EXISTS idx_pois_status_verification ON spatial_schema.pois(status, verification_level);

-- Indexes for democratic categorization
CREATE INDEX IF NOT EXISTS idx_poi_categories_poi_id ON spatial_schema.poi_categories(poi_id);
CREATE INDEX IF NOT EXISTS idx_poi_categories_votes_name ON spatial_schema.poi_categories(total_votes DESC, category_name);
CREATE INDEX IF NOT EXISTS idx_master_categories_active_name ON spatial_schema.master_categories(is_active, category_name);
CREATE INDEX IF NOT EXISTS idx_master_categories_parent ON spatial_schema.master_categories(parent_category_id);

-- Indexes for POI category votes
CREATE INDEX IF NOT EXISTS idx_poi_category_votes_poi_id ON spatial_schema.poi_category_votes(poi_id);
CREATE INDEX IF NOT EXISTS idx_poi_category_votes_user_id ON spatial_schema.poi_category_votes(user_id);
CREATE INDEX IF NOT EXISTS idx_poi_category_votes_category ON spatial_schema.poi_category_votes(category_name);
CREATE INDEX IF NOT EXISTS idx_poi_category_votes_vote_type ON spatial_schema.poi_category_votes(vote_type);
CREATE INDEX IF NOT EXISTS idx_poi_category_votes_poi_category ON spatial_schema.poi_category_votes(poi_id, category_name, vote_type);

-- Indexes for community reviews
CREATE INDEX IF NOT EXISTS idx_community_reviews_poi_id ON spatial_schema.community_reviews(poi_id);
CREATE INDEX IF NOT EXISTS idx_community_reviews_user_id ON spatial_schema.community_reviews(user_id);
CREATE INDEX IF NOT EXISTS idx_community_reviews_rating ON spatial_schema.community_reviews(rating);
CREATE INDEX IF NOT EXISTS idx_community_reviews_created_at ON spatial_schema.community_reviews(created_at);
CREATE INDEX IF NOT EXISTS idx_community_reviews_poi_rating_date ON spatial_schema.community_reviews(poi_id, rating DESC, created_at DESC);
CREATE INDEX IF NOT EXISTS idx_community_reviews_title ON spatial_schema.community_reviews USING gin(to_tsvector('english', review_title));
CREATE INDEX IF NOT EXISTS idx_community_reviews_tags ON spatial_schema.community_reviews USING gin(tags);

-- Indexes for POI tags
CREATE INDEX IF NOT EXISTS idx_poi_tags_poi_id ON spatial_schema.poi_tags(poi_id);
CREATE INDEX IF NOT EXISTS idx_poi_tags_tag_name ON spatial_schema.poi_tags(tag_name);
CREATE INDEX IF NOT EXISTS idx_poi_tags_poi_type ON spatial_schema.poi_tags(poi_id, tag_type);
CREATE INDEX IF NOT EXISTS idx_poi_tags_vote_count ON spatial_schema.poi_tags(vote_count DESC);

-- Indexes for verification tasks
CREATE INDEX IF NOT EXISTS idx_verification_tasks_poi_id ON spatial_schema.verification_tasks(poi_id);
CREATE INDEX IF NOT EXISTS idx_verification_tasks_status ON spatial_schema.verification_tasks(status);
CREATE INDEX IF NOT EXISTS idx_verification_tasks_assigned_to ON spatial_schema.verification_tasks(assigned_to);
CREATE INDEX IF NOT EXISTS idx_verification_tasks_status_priority_expires ON spatial_schema.verification_tasks(status, priority DESC, expires_at);
CREATE INDEX IF NOT EXISTS idx_verification_tasks_assigned_status ON spatial_schema.verification_tasks(assigned_to, status);

-- Indexes for POI media
CREATE INDEX IF NOT EXISTS idx_poi_media_poi_id ON spatial_schema.poi_media(poi_id);
CREATE INDEX IF NOT EXISTS idx_poi_media_user_id ON spatial_schema.poi_media(user_id);
CREATE INDEX IF NOT EXISTS idx_poi_media_type ON spatial_schema.poi_media(media_type);
CREATE INDEX IF NOT EXISTS idx_poi_media_created_at ON spatial_schema.poi_media(created_at);
CREATE INDEX IF NOT EXISTS idx_poi_media_likes_media_id ON spatial_schema.poi_media_likes(media_id);
CREATE INDEX IF NOT EXISTS idx_poi_media_likes_user_id ON spatial_schema.poi_media_likes(user_id);
CREATE INDEX IF NOT EXISTS idx_poi_media_favorites_media_id ON spatial_schema.poi_media_favorites(media_id);
CREATE INDEX IF NOT EXISTS idx_poi_media_favorites_user_id ON spatial_schema.poi_media_favorites(user_id);

-- Indexes for roads table
CREATE INDEX IF NOT EXISTS idx_roads_geom ON spatial_schema.roads USING GIST (geom);
CREATE INDEX IF NOT EXISTS idx_roads_osm_id ON spatial_schema.roads(osm_id);
CREATE INDEX IF NOT EXISTS idx_roads_highway ON spatial_schema.roads(highway);
CREATE INDEX IF NOT EXISTS idx_roads_name ON spatial_schema.roads(name);
CREATE INDEX IF NOT EXISTS idx_roads_oneway ON spatial_schema.roads(oneway);
CREATE INDEX IF NOT EXISTS idx_roads_highway_oneway ON spatial_schema.roads(highway, oneway);
