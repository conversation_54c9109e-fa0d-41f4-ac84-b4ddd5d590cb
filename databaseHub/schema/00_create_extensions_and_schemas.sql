-- 00_create_extensions_and_schemas.sql
-- Create required extensions and schemas for Wizlop

-- Extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS postgis;

-- Schemas
CREATE SCHEMA IF NOT EXISTS backend_schema;
CREATE SCHEMA IF NOT EXISTS spatial_schema;
-- Add other schemas as needed

-- (Optional) Create NextAuth global tables here if needed (see 03_nextauth_global.sql)

-- Set search_path for the database
-- (You may need to run this as a superuser or in psql)
-- ALTER DATABASE wizlop_db SET search_path TO backend_schema, spatial_schema, public; 