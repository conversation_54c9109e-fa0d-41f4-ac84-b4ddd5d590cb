#!/bin/bash

# reset_database.sh - Drop and recreate Wizlop database with correct schema sequence
# Usage: ./scripts/reset_database.sh

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Database configuration
DB_NAME="wizlop_db"
DB_USER="wizlop_user"
DB_HOST="localhost"
DB_PORT="5432"
SUPERUSER="saidmustafa"  # PostgreSQL superuser (detected from system)

echo -e "${BLUE}🗃️  Wizlop Database Reset Script${NC}"
echo "=================================="

# Check if we're in the right directory
if [ ! -f "schema/00_create_extensions_and_schemas.sql" ]; then
    echo -e "${RED}❌ Error: Please run this script from the databaseHub directory${NC}"
    echo "Current directory: $(pwd)"
    exit 1
fi

# Confirmation prompt
echo -e "${YELLOW}⚠️  WARNING: This will completely drop and recreate the database!${NC}"
echo "Database: $DB_NAME"
echo "User: $DB_USER"
echo "Host: $DB_HOST"
echo ""
read -p "Are you sure you want to continue? (y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo -e "${YELLOW}❌ Operation cancelled${NC}"
    exit 1
fi

echo ""
echo -e "${BLUE}🔄 Starting database reset...${NC}"

# Step 1: Drop existing database
echo -e "${YELLOW}1. Dropping existing database...${NC}"
psql -U $SUPERUSER -h $DB_HOST -d postgres -c "DROP DATABASE IF EXISTS $DB_NAME;" || {
    echo -e "${RED}❌ Failed to drop database. Make sure PostgreSQL is running and you have permissions.${NC}"
    exit 1
}
echo -e "${GREEN}✅ Database dropped${NC}"

# Step 2: Drop and recreate user
echo -e "${YELLOW}2. Recreating database user...${NC}"
psql -U $SUPERUSER -h $DB_HOST -d postgres -c "DROP USER IF EXISTS $DB_USER;" || true
psql -U $SUPERUSER -h $DB_HOST -d postgres -c "CREATE USER $DB_USER WITH PASSWORD 'wizlop_pass';" || {
    echo -e "${RED}❌ Failed to create user${NC}"
    exit 1
}
echo -e "${GREEN}✅ User recreated${NC}"

# Step 3: Create database
echo -e "${YELLOW}3. Creating database...${NC}"
psql -U $SUPERUSER -h $DB_HOST -d postgres -c "CREATE DATABASE $DB_NAME OWNER $DB_USER;" || {
    echo -e "${RED}❌ Failed to create database${NC}"
    exit 1
}
echo -e "${GREEN}✅ Database created${NC}"

# Step 4: Grant permissions
echo -e "${YELLOW}4. Granting permissions...${NC}"
psql -U $SUPERUSER -h $DB_HOST -d postgres -c "GRANT ALL PRIVILEGES ON DATABASE $DB_NAME TO $DB_USER;" || {
    echo -e "${RED}❌ Failed to grant permissions${NC}"
    exit 1
}
echo -e "${GREEN}✅ Permissions granted${NC}"

# Step 5: Run schema files in correct order
echo -e "${YELLOW}5. Running schema migrations in correct order...${NC}"

SCHEMA_FILES=(
    "schema/00_create_extensions_and_schemas.sql"
    "schema/backend_schema.sql"
    "schema/spatial_schema.sql"
    "schema/cross_schema.sql"
    "schema/top_location.sql"
)

for schema_file in "${SCHEMA_FILES[@]}"; do
    if [ -f "$schema_file" ]; then
        echo -e "${BLUE}   📄 Running: $schema_file${NC}"
        psql -U $DB_USER -h $DB_HOST -d $DB_NAME -f "$schema_file" || {
            echo -e "${RED}❌ Failed to run $schema_file${NC}"
            echo -e "${RED}   Check the file for syntax errors or missing dependencies${NC}"
            exit 1
        }
        echo -e "${GREEN}   ✅ Completed: $schema_file${NC}"
    else
        echo -e "${RED}❌ Schema file not found: $schema_file${NC}"
        exit 1
    fi
done

# Step 6: Verify tables were created
echo -e "${YELLOW}6. Verifying table creation...${NC}"
CRITICAL_TABLES=(
    "backend_schema.nextauth_users"
    "spatial_schema.pois"
    "spatial_schema.user_pois_temp"
    "spatial_schema.user_pois_approved"
)

for table in "${CRITICAL_TABLES[@]}"; do
    if psql -U $DB_USER -h $DB_HOST -d $DB_NAME -c "\d $table" > /dev/null 2>&1; then
        echo -e "${GREEN}   ✅ Table exists: $table${NC}"
    else
        echo -e "${RED}   ❌ Table missing: $table${NC}"
        exit 1
    fi
done

echo ""
echo -e "${GREEN}🎉 Database reset completed successfully!${NC}"
echo ""
echo -e "${BLUE}📋 Summary:${NC}"
echo "   • Database: $DB_NAME"
echo "   • User: $DB_USER"
echo "   • Host: $DB_HOST"
echo "   • Schemas: backend_schema, spatial_schema, public"
echo "   • Critical tables verified"
echo ""
echo -e "${BLUE}🚀 Next steps:${NC}"
echo "   1. Import spatial data (admin boundaries, roads, POIs)"
echo "   2. Run application tests"
echo "   3. Check application connectivity"
echo ""
echo -e "${GREEN}✅ Ready to use!${NC}"
