#!/usr/bin/env python3
"""
Import POIs into the spatial database from the merged CSV.

This script imports POI data from all_cities_cleaned.csv into the spatial_schema.pois table.
It handles the mapping between CSV columns and database columns, validates coordinates,
and provides progress tracking during import.

Updated to support the current schema structure with proper column mapping.
"""
import csv
from pathlib import Path
import psycopg2
import logging
import os
from dotenv import load_dotenv

logging.basicConfig(level=logging.INFO,
                    format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Always load .env from the root of databaseHub
env_path = Path(__file__).resolve().parents[4] / '.env'
load_dotenv(dotenv_path=env_path)

DB_CONFIG = {
    'dbname': os.getenv('DB_NAME'),
    'user': os.getenv('DB_USER'),
    'password': os.getenv('DB_PASSWORD'),
    'host': os.getenv('DB_HOST'),
    'port': os.getenv('DB_PORT', '5432')
}

# Path to the merged CSV (now in databaseHub/scripts/spatial/data/poi)
MERGED_CSV = Path(__file__).resolve().parent.parent.parent / \
    'data' / 'poi' / 'all_cities_merged.csv'

# List of columns in the merged CSV (current structure)
CSV_COLUMNS = [
    'latitude', 'longitude', 'city', 'district', 'neighborhood', 'street', 'phone_number', 'description',
    'opening_hours', 'cuisine', 'name', 'name_en', 'name_uk', 'name_de', 'name_tr', 'name_ru', 'name_ar',
    'category', 'subcategory', 'full_address', 'province', 'country'
]

# Database columns that correspond to CSV data (only the ones we'll insert)
# Note: The database has 55+ columns, but we only insert data for these columns
# Other columns will use their default values
DB_COLUMNS = [
    'name', 'name_en', 'name_tr', 'name_uk', 'name_de', 'name_ru', 'name_ar',
    'category', 'subcategory', 'cuisine', 'city', 'district', 'neighborhood',
    'street', 'full_address', 'province', 'country',
    'latitude', 'longitude', 'phone_number', 'opening_hours', 'description', 'geom'
]


def validate_csv_structure():
    """Validate that the CSV has the expected columns."""
    if not MERGED_CSV.exists():
        logger.error(f"CSV file not found: {MERGED_CSV}")
        return False

    with open(MERGED_CSV, 'r', encoding='utf-8') as f:
        reader = csv.DictReader(f)
        csv_headers = reader.fieldnames

        missing_columns = set(CSV_COLUMNS) - set(csv_headers)
        if missing_columns:
            logger.error(f"Missing required columns in CSV: {missing_columns}")
            return False

        extra_columns = set(csv_headers) - set(CSV_COLUMNS)
        if extra_columns:
            logger.info(
                f"Extra columns in CSV (will be ignored): {extra_columns}")

        logger.info(
            f"CSV validation passed. Found {len(csv_headers)} columns.")
        return True


def import_pois():
    # Validate CSV structure first
    if not validate_csv_structure():
        return

    # First, count total rows for progress tracking
    with open(MERGED_CSV, 'r', encoding='utf-8') as f:
        total_rows = sum(1 for line in f) - 1  # Subtract header row

    logger.info(f"Starting import of {total_rows} POIs from CSV...")

    conn = psycopg2.connect(**DB_CONFIG)
    conn.autocommit = True
    cursor = conn.cursor()

    imported = 0
    skipped = 0

    with open(MERGED_CSV, 'r', encoding='utf-8') as f:
        reader = csv.DictReader(f)
        for row_num, row in enumerate(reader, 1):
            try:
                # Extract and validate coordinates
                lat = float(row['latitude']) if row['latitude'] else None
                lon = float(row['longitude']) if row['longitude'] else None

                if lat is None or lon is None:
                    skipped += 1
                    continue

                # Prepare values in the order of DB_COLUMNS
                values = []
                for col in DB_COLUMNS[:-1]:  # All columns except 'geom'
                    if col in CSV_COLUMNS:
                        # Get value from CSV
                        value = row.get(col, None)
                        # Convert empty strings to None
                        values.append(
                            value if value and value.strip() else None)
                    else:
                        # Column not in CSV, use None (will use default value)
                        values.append(None)

                # Add geometry as last value
                geom_wkt = f'SRID=4326;POINT({lon} {lat})'
                values.append(geom_wkt)

                # Build the SQL insert statement
                placeholders = ', '.join(['%s'] * (len(DB_COLUMNS) - 1))
                db_columns_str = ', '.join(DB_COLUMNS)

                insert_sql = f"""
                INSERT INTO spatial_schema.pois ({db_columns_str})
                VALUES ({placeholders}, ST_GeomFromText(%s))
                """

                cursor.execute(insert_sql, values)
                imported += 1

                # Progress tracking
                if imported % 1000 == 0:
                    progress = (row_num / total_rows) * 100
                    logger.info(
                        f"Progress: {progress:.1f}% - Imported {imported} POIs, Skipped {skipped}")

            except Exception as e:
                logger.warning(f"Failed to import POI at row {row_num}: {e}")
                skipped += 1
                continue

    logger.info(
        f"Import completed! Imported: {imported}, Skipped: {skipped}, Total processed: {imported + skipped}")
    cursor.close()
    conn.close()


if __name__ == '__main__':
    logger.info("Starting POI import process...")
    logger.info(f"CSV file: {MERGED_CSV}")
    logger.info(f"Expected CSV columns: {len(CSV_COLUMNS)}")
    logger.info(
        f"Database columns to insert: {len(DB_COLUMNS)} (out of 55+ total columns)")

    # Check if CSV file exists first
    if not MERGED_CSV.exists():
        logger.error(f"CSV file not found: {MERGED_CSV}")
        logger.error(
            "Please ensure the POI data file exists before running import.")
        logger.info("You may need to run data extraction scripts first.")
    else:
        import_pois()

    logger.info("POI import process completed.")
